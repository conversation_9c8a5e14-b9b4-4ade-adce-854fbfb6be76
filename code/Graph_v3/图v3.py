# -*- coding: utf-8 -*-
"""
小说人物档案管理系统 v3.0 - 定位为辅助创作工具
核心特性：
1) 人物关系使用“名字”为节点ID；关系图为有向图（支持非对称关系）
2) 人物节点具备多维属性（核心身份/内在维度/外在表现/角色类型等）
3) 全局事件库：事件可包含多人物参与，提供人/事/地快速查询索引
4) 角色成长轨迹：独立的里程碑系统，支持与事件绑定；不混入关系图/事件图
5) 兼容v2数据，提供迁移入口
"""

from __future__ import annotations
import json
import uuid
from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple, Set
from collections import defaultdict
import networkx as nx

# ============== 通用基础 ==============

def now_iso() -> str:
    return datetime.now().isoformat()

class BaseManager:
    def __init__(self):
        self.created_time = datetime.now()
        self.updated_time = datetime.now()

    def _generate_id(self, prefix: str = "") -> str:
        return f"{prefix}_{uuid.uuid4().hex[:8]}" if prefix else uuid.uuid4().hex[:8]

    def _update_timestamp(self):
        self.updated_time = datetime.now()

# ============== 人物关系（有向，名字为节点ID） ==============

class CharacterGraphV3(BaseManager):
    """名字即节点ID；关系为有向图。支持非对称关系。
    - 去重：若同名出现，自动添加 #2/#3 后缀保证唯一
    - 保留一个稳定的 uid（迁移/兼容用），但图节点ID使用 name_key
    """
    RELATIONSHIP_TYPES = {
        '师徒': {'color': '#FF6B6B'},
        '朋友': {'color': '#4ECDC4'},
        '敌人': {'color': '#FF4757'},
        '恋人': {'color': '#FF3838'},
        '亲人': {'color': '#FFA502'},
        '同事': {'color': '#70A1FF'},
        '陌生人': {'color': '#A4B0BE'}
    }

    def __init__(self):
        super().__init__()
        self.graph = nx.DiGraph()
        self.name_to_uid: Dict[str, str] = {}   # name_key -> uid
        self.uid_to_name: Dict[str, str] = {}   # uid -> name_key
        self.alias_to_name: Dict[str, str] = {} # 别名 -> name_key

    @staticmethod
    def _normalize_name(name: str) -> str:
        return (name or "未命名").strip()

    def _ensure_unique_name(self, name: str) -> str:
        base = self._normalize_name(name)
        if base not in self.name_to_uid:
            return base
        # 追加序号避免冲突
        i = 2
        while f"{base}#{i}" in self.name_to_uid:
            i += 1
        return f"{base}#{i}"

    def add_character(self, character_data: Dict[str, Any]) -> str:
        """返回 name_key（作为节点ID）。"""
        raw_name = character_data.get('name', '未命名')
        name_key = self._ensure_unique_name(raw_name)
        uid = character_data.get('uid') or self._generate_id('char')

        node_attrs = {
            'uid': uid,
            'name': name_key,
            'prototype': character_data.get('prototype', ''),            # 核心身份-原型
            'narrative_role': character_data.get('narrative_role', ''),  # 角色类型/叙事位置
            'core_identity': character_data.get('core_identity', {}),
            'internal_dimension': character_data.get('internal_dimension', {}),
            'external_dimension': character_data.get('external_dimension', {}),
            'tags': character_data.get('tags', []),
            'created_time': now_iso(),
            'updated_time': now_iso(),
        }

        self.name_to_uid[name_key] = uid
        self.uid_to_name[uid] = name_key
        self.graph.add_node(name_key, **node_attrs)
        self._update_timestamp()
        print(f"✅ 添加人物: {name_key} (uid: {uid})")
        return name_key

    def add_alias(self, name_key: str, alias: str) -> bool:
        alias = alias.strip()
        if not self.graph.has_node(name_key):
            return False
        self.alias_to_name[alias] = name_key
        return True

    def resolve_name(self, name_or_alias: str) -> Optional[str]:
        name_or_alias = (name_or_alias or '').strip()
        if name_or_alias in self.graph.nodes:
            return name_or_alias
        return self.alias_to_name.get(name_or_alias)

    def add_relationship(self, src_name: str, dst_name: str, relationship_data: Dict[str, Any]) -> str:
        src = self.resolve_name(src_name)
        dst = self.resolve_name(dst_name)
        if not (src and dst and self.graph.has_node(src) and self.graph.has_node(dst)):
            print(f"❌ 人物不存在: {src_name} 或 {dst_name}")
            return ""

        rel_id = relationship_data.get('id', self._generate_id('rel'))
        rel_type = relationship_data.get('relationship_type', '朋友')
        cfg = self.RELATIONSHIP_TYPES.get(rel_type, {'color': '#97C2FC'})
        edge_attrs = {
            'id': rel_id,
            'relationship_type': rel_type,
            'strength': relationship_data.get('strength', 5),
            'status': relationship_data.get('status', '正常'),
            'description': relationship_data.get('description', ''),
            'color': cfg['color'],
            'created_time': now_iso(),
            'updated_time': now_iso(),
        }
        self.graph.add_edge(src, dst, **edge_attrs)
        self._update_timestamp()
        print(f"✅ 添加关系: {src} → {dst} ({rel_type})")
        return rel_id

    def get_character(self, name_or_alias: str) -> Optional[Dict[str, Any]]:
        name_key = self.resolve_name(name_or_alias)
        if not name_key:
            return None
        return self.graph.nodes[name_key]

    def get_outgoing_relationships(self, name_or_alias: str) -> List[Dict[str, Any]]:
        name_key = self.resolve_name(name_or_alias)
        if not name_key or not self.graph.has_node(name_key):
            return []
        res = []
        for _, dst, data in self.graph.out_edges(name_key, data=True):
            res.append({'source': name_key, 'target': dst, **data})
        return res

    def get_incoming_relationships(self, name_or_alias: str) -> List[Dict[str, Any]]:
        name_key = self.resolve_name(name_or_alias)
        if not name_key or not self.graph.has_node(name_key):
            return []
        res = []
        for src, _, data in self.graph.in_edges(name_key, data=True):
            res.append({'source': src, 'target': name_key, **data})
        return res

    def analyze_centrality(self) -> Dict[str, float]:
        # 使用有向图的入度中心性作为影响力近似
        centrality = nx.in_degree_centrality(self.graph)
        return {n: centrality.get(n, 0) for n in self.graph.nodes}

# ============== 全局事件库（含多人物参与与快速索引） ==============

class EventLibraryV3(BaseManager):
    def __init__(self):
        super().__init__()
        self.events: Dict[str, Dict[str, Any]] = {}
        # 参与数据直接挂在事件上
        # 索引结构
        self.index_by_person: Dict[str, Set[str]] = defaultdict(set)  # name_key -> {event_id}
        self.index_by_location: Dict[str, Set[str]] = defaultdict(set)
        self.index_by_tag: Dict[str, Set[str]] = defaultdict(set)

    def add_event(self, event_data: Dict[str, Any]) -> str:
        event_id = event_data.get('id', self._generate_id('event'))
        ev = {
            'id': event_id,
            'name': event_data.get('name', '未命名事件'),
            'description': event_data.get('description', ''),
            'timestamp': event_data.get('timestamp', now_iso()),
            'location': event_data.get('location', ''),
            'tags': list(event_data.get('tags', [])),
            'importance': event_data.get('importance', 5),
            'participants': [],  # list of {name, role, impact, note}
            'created_time': now_iso(),
            'updated_time': now_iso(),
        }
        self.events[event_id] = ev
        # 建立位置/标签索引
        if ev['location']:
            self.index_by_location[ev['location']].add(event_id)
        for t in ev['tags']:
            self.index_by_tag[t].add(event_id)
        self._update_timestamp()
        print(f"✅ 添加事件: {ev['name']} (ID: {event_id})")
        return event_id

    def add_participant(self, event_id: str, character_name: str, role: str = '参与者',
                        impact: int = 5, note: str = '') -> bool:
        ev = self.events.get(event_id)
        if not ev:
            return False
        entry = {
            'name': character_name,
            'role': role,
            'emotional_impact': impact,
            'note': note,
        }
        ev['participants'].append(entry)
        self.index_by_person[character_name].add(event_id)
        self._update_timestamp()
        return True

    def get_event(self, event_id: str) -> Optional[Dict[str, Any]]:
        return self.events.get(event_id)

    def query_events(self, persons: Optional[List[str]] = None,
                     keywords: Optional[List[str]] = None,
                     location: Optional[str] = None,
                     time_range: Optional[Tuple[str, str]] = None) -> List[Dict[str, Any]]:
        candidates: Set[str] = set(self.events.keys())
        # 人
        if persons:
            sets = [self.index_by_person.get(p, set()) for p in persons]
            if not sets:
                return []
            inter = sets[0].copy()
            for s in sets[1:]:
                inter &= s
            candidates &= inter
        # 地
        if location:
            candidates &= self.index_by_location.get(location, set())
        # 事（关键词/标签/名称/描述）
        if keywords:
            kw = [k.lower() for k in keywords]
            def match(ev: Dict[str, Any]) -> bool:
                text = (ev['name'] + ' ' + ev['description'] + ' ' + ' '.join(ev['tags'])).lower()
                return all(k in text for k in kw)
            candidates = {eid for eid in candidates if match(self.events[eid])}
        # 时间
        if time_range:
            start = datetime.fromisoformat(time_range[0])
            end = datetime.fromisoformat(time_range[1])
            def in_range(ev: Dict[str, Any]) -> bool:
                try:
                    t = datetime.fromisoformat(ev['timestamp'])
                except Exception:
                    return False
                return start <= t <= end
            candidates = {eid for eid in candidates if in_range(self.events[eid])}
        return [self.events[eid] for eid in sorted(candidates)]

# ============== 里程碑系统（独立，支持事件绑定） ==============

class MilestoneManagerV3(BaseManager):
    def __init__(self):
        super().__init__()
        self.graph = nx.DiGraph()  # 里程碑依赖
        self.milestones: Dict[str, Dict[str, Any]] = {}
        self.by_character: Dict[str, List[str]] = defaultdict(list)  # name_key -> [milestone_id]

    def add_milestone(self, milestone_data: Dict[str, Any]) -> str:
        mid = milestone_data.get('id', self._generate_id('milestone'))
        name_key = milestone_data.get('character_name', '')
        m = {
            'id': mid,
            'character_name': name_key,
            'name': milestone_data.get('name', '未命名里程碑'),
            'description': milestone_data.get('description', ''),
            'timestamp': milestone_data.get('timestamp', now_iso()),
            'milestone_type': milestone_data.get('milestone_type', 'growth'),
            'psychological_change': milestone_data.get('psychological_change', {}),
            'values_change': milestone_data.get('values_change', {}),
            'goals_change': milestone_data.get('goals_change', {}),
            'proof': milestone_data.get('proof', ''),
            'importance': milestone_data.get('importance', 5),
            'bound_events': list(milestone_data.get('bound_events', [])),  # 新增绑定
            'created_time': now_iso(),
            'updated_time': now_iso(),
        }
        self.milestones[mid] = m
        self.graph.add_node(mid, **m)
        if name_key:
            self.by_character[name_key].append(mid)
        self._update_timestamp()
        print(f"✅ 添加里程碑: {m['name']} (人物: {name_key})")
        return mid

    def bind_milestone_to_event(self, milestone_id: str, event_id: str) -> bool:
        m = self.milestones.get(milestone_id)
        if not m:
            return False
        if event_id not in m['bound_events']:
            m['bound_events'].append(event_id)
            self.graph.nodes[milestone_id]['bound_events'] = m['bound_events']
            self._update_timestamp()
        return True

    def add_dependency(self, pre_id: str, dep_id: str, data: Optional[Dict[str, Any]] = None) -> bool:
        if not (pre_id in self.milestones and dep_id in self.milestones):
            return False
        d = data or {}
        self.graph.add_edge(pre_id, dep_id, **{
            'id': d.get('id', self._generate_id('dep')),
            'dependency_type': d.get('dependency_type', 'prerequisite'),
            'strength': d.get('strength', 5),
            'description': d.get('description', ''),
            'created_time': now_iso(),
        })
        self._update_timestamp()
        return True

    def get_character_milestones(self, name_key: str) -> List[Dict[str, Any]]:
        ids = self.by_character.get(name_key, [])
        arr = [self.milestones[i] for i in ids if i in self.milestones]
        arr.sort(key=lambda x: x['timestamp'])
        return arr

# ============== 系统聚合与统一接口 ==============

class NovelV3System:
    def __init__(self):
        print("🚀 初始化小说人物档案管理系统 v3.0 ...")
        self.characters = CharacterGraphV3()
        self.events = EventLibraryV3()
        self.milestones = MilestoneManagerV3()
        self.system_info = {
            'version': '3.0',
            'created_time': now_iso(),
            'updated_time': now_iso(),
            'description': '辅助创作：人物塑造/剧情连接/事件记录'
        }
        print("✅ v3 系统初始化完成")

    def _touch(self):
        self.system_info['updated_time'] = now_iso()

    # ---- 统一新增接口 ----
    def add_character(self, character_data: Dict[str, Any]) -> str:
        name_key = self.characters.add_character(character_data)
        self._touch()
        return name_key

    def add_relationship(self, src_name: str, dst_name: str, relationship_data: Dict[str, Any]) -> str:
        rid = self.characters.add_relationship(src_name, dst_name, relationship_data)
        self._touch()
        return rid

    def add_event(self, event_data: Dict[str, Any]) -> str:
        eid = self.events.add_event(event_data)
        self._touch()
        return eid

    def add_participant(self, event_id: str, character_name: str, role: str = '参与者',
                        impact: int = 5, note: str = '') -> bool:
        # 允许传入别名；解析为标准 name_key
        resolved = self.characters.resolve_name(character_name)
        final_name = resolved or (character_name or '').strip()
        ok = self.events.add_participant(event_id, final_name, role, impact, note)
        self._touch()
        return ok

    def add_milestone(self, milestone_data: Dict[str, Any]) -> str:
        mid = self.milestones.add_milestone(milestone_data)
        self._touch()
        return mid

    def bind_milestone_to_event(self, milestone_id: str, event_id: str) -> bool:
        ok = self.milestones.bind_milestone_to_event(milestone_id, event_id)
        self._touch()
        return ok

    # ---- 查询接口 ----
    def get_character_complete_info(self, name_or_alias: str) -> Dict[str, Any]:
        base = self.characters.get_character(name_or_alias)
        if not base:
            return {}
        name_key = base['name']
        info = {
            'basic_info': base,
            'outgoing_relationships': self.characters.get_outgoing_relationships(name_key),
            'incoming_relationships': self.characters.get_incoming_relationships(name_key),
            'milestones': self.milestones.get_character_milestones(name_key),
            'events_participated': [
                self.events.events[eid] for eid in sorted(self.events.index_by_person.get(name_key, set()))
            ],
            'analysis': {
                'centrality': self.characters.analyze_centrality().get(name_key, 0.0)
            }
        }
        return info

    def get_event_complete_info(self, event_id: str) -> Dict[str, Any]:
        ev = self.events.get_event(event_id)
        return ev or {}

    def query_three_elements(self, person: Optional[str] = None,
                             matter_keywords: Optional[List[str]] = None,
                             place: Optional[str] = None,
                             time_range: Optional[Tuple[str, str]] = None) -> List[Dict[str, Any]]:
        persons = [person] if person else None
        return self.events.query_events(persons=persons, keywords=matter_keywords,
                                        location=place, time_range=time_range)

    # ---- v2 -> v3 数据迁移 ----
    def import_from_v2(self, v2_system) -> Dict[str, Any]:
        """接受 v2 的 NovelMultiGraphSystem 或测试版，迁移到 v3。
        返回映射表：{'char_id_to_name': {...}, 'event_id_map': {...}}
        """
        mapping = {
            'char_id_to_name': {},
            'event_id_map': {},
        }
        # 人物
        for old_id, c in getattr(v2_system.character_manager, 'characters', {}).items():
            name_key = self.add_character(c)  # 会自动唯一化
            mapping['char_id_to_name'][old_id] = name_key
        # 关系（v2多为无向，导入为双向有向）
        g = v2_system.character_manager.graph
        for u, v, data in g.edges(data=True):
            src = mapping['char_id_to_name'].get(u)
            dst = mapping['char_id_to_name'].get(v)
            if not (src and dst):
                continue
            self.add_relationship(src, dst, data)
            self.add_relationship(dst, src, data)
        # 事件
        for eid, ev in getattr(v2_system.event_manager, 'events', {}).items():
            new_id = self.add_event(ev)
            mapping['event_id_map'][eid] = new_id
        # 参与（如果 v2 存在 ParticipationManager）
        part_mgr = getattr(v2_system, 'participation_manager', None)
        if part_mgr:
            for _, pdata in getattr(part_mgr, 'particiations', {}).items():  # 兼容性容错
                cid = pdata.get('character_id')
                old_eid = pdata.get('event_id')
                name_key = mapping['char_id_to_name'].get(cid)
                new_eid = mapping['event_id_map'].get(old_eid)
                if name_key and new_eid:
                    self.add_participant(new_eid, name_key, pdata.get('role', '参与者'),
                                         pdata.get('emotional_impact', 5), pdata.get('impact_description', ''))
        # 里程碑（如果 v2 有）
        ms_mgr = getattr(v2_system, 'milestone_manager', None)
        if ms_mgr:
            for mid, m in getattr(ms_mgr, 'milestones', {}).items():
                name_key = mapping['char_id_to_name'].get(m.get('character_id')) or m.get('character_name', '')
                new_mid = self.add_milestone({**m, 'character_name': name_key})
                # 绑定事件（若 v2 数据存在 event_id 字段）
                ev_id = m.get('event_id')
                if ev_id:
                    new_eid = mapping['event_id_map'].get(ev_id)
                    if new_eid:
                        self.bind_milestone_to_event(new_mid, new_eid)
        return mapping

    # ---- 导入/导出 ----
    def export_to_json(self, filename: str = 'novel_v3_data.json') -> bool:
        try:
            data = {
                'system_info': self.system_info,
                'characters': {
                    'nodes': list(self.characters.graph.nodes(data=True)),
                    'edges': list(self.characters.graph.edges(data=True)),
                    'alias': self.characters.alias_to_name,
                },
                'events': self.events.events,
                'indices': {
                    'by_person': {k: list(v) for k, v in self.events.index_by_person.items()},
                    'by_location': {k: list(v) for k, v in self.events.index_by_location.items()},
                    'by_tag': {k: list(v) for k, v in self.events.index_by_tag.items()},
                },
                'milestones': {
                    'nodes': self.milestones.milestones,
                    'edges': list(self.milestones.graph.edges(data=True)),
                },
                'export_time': now_iso(),
            }
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
            print(f"✅ v3 数据导出完成: {filename}")
            return True
        except Exception as e:
            print(f"❌ 导出失败: {e}")
            return False

    def import_from_json(self, filename: str = 'novel_v3_data.json') -> bool:
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            # characters
            self.characters.graph = nx.DiGraph()
            self.characters.alias_to_name = data['characters'].get('alias', {})
            self.characters.name_to_uid.clear(); self.characters.uid_to_name.clear()
            for name_key, attrs in data['characters']['nodes']:
                self.characters.graph.add_node(name_key, **attrs)
                uid = attrs.get('uid', '')
                if uid:
                    self.characters.name_to_uid[name_key] = uid
                    self.characters.uid_to_name[uid] = name_key
            for src, dst, ed in data['characters']['edges']:
                self.characters.graph.add_edge(src, dst, **ed)
            # events
            self.events.events = data['events']
            self.events.index_by_person = defaultdict(set, {k: set(v) for k, v in data['indices']['by_person'].items()})
            self.events.index_by_location = defaultdict(set, {k: set(v) for k, v in data['indices']['by_location'].items()})
            self.events.index_by_tag = defaultdict(set, {k: set(v) for k, v in data['indices']['by_tag'].items()})
            # milestones
            self.milestones.graph = nx.DiGraph()
            self.milestones.milestones = data['milestones']['nodes']
            self.milestones.by_character.clear()
            for mid, m in self.milestones.milestones.items():
                self.milestones.graph.add_node(mid, **m)
                nk = m.get('character_name', '')
                if nk:
                    self.milestones.by_character[nk].append(mid)
            for pre, dep, ed in data['milestones']['edges']:
                self.milestones.graph.add_edge(pre, dep, **ed)
            print(f"✅ v3 数据导入完成: {filename}")
            return True
        except Exception as e:
            print(f"❌ 导入失败: {e}")
            return False

    # ---- 一致性检查与便捷查询/示例 ----
    def run_consistency_checks(self, rules: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        rules = rules or {}
        report = {'errors': [], 'warnings': [], 'info': []}
        self._check_participant_names(report)
        self._check_milestone_time_order(report)
        self._check_relationship_conflicts(report, rules)
        report['summary'] = {
            'errors': len(report['errors']),
            'warnings': len(report['warnings']),
            'info': len(report['info'])
        }
        return report

    def _check_participant_names(self, report: Dict[str, List[str]]):
        known = set(self.characters.graph.nodes) | set(self.characters.alias_to_name.keys())
        for eid, ev in self.events.events.items():
            for p in ev.get('participants', []):
                name = (p.get('name') or '').strip()
                if name and name not in known:
                    report['warnings'].append(f"事件[{ev.get('name')}] 参与者未建档: {name}")

    def _check_milestone_time_order(self, report: Dict[str, List[str]]):
        for name_key, mids in self.milestones.by_character.items():
            prev = None
            for mid in mids:
                m = self.milestones.milestones.get(mid)
                if not m:
                    continue
                try:
                    ts = datetime.fromisoformat(m.get('timestamp'))
                except Exception:
                    report['errors'].append(f"里程碑时间格式错误: {name_key} - {m.get('name')} - {m.get('timestamp')}")
                    continue
                if prev and ts < prev:
                    report['warnings'].append(f"里程碑时间倒序: {name_key} - {m.get('name')}")
                prev = ts

    def _check_relationship_conflicts(self, report: Dict[str, List[str]], rules: Dict[str, Any]):
        conflict_pairs = {('恋人','敌人'), ('朋友','敌人')}
        threshold = int(rules.get('conflict_strength', 7))
        g = self.characters.graph
        for u, v in g.edges():
            d = g[u][v]
            t = d.get('relationship_type')
            rev = g[v][u] if g.has_edge(v, u) else None
            if not rev:
                continue
            t2 = rev.get('relationship_type')
            if (t, t2) in conflict_pairs or (t2, t) in conflict_pairs:
                s1 = int(d.get('strength', 0))
                s2 = int(rev.get('strength', 0))
                if s1 >= threshold and s2 >= threshold:
                    report['warnings'].append(f"关系冲突: {u}↔{v} ({t} vs {t2}, 强度 {s1}/{s2})")

    # 便捷查询封装
    def query_by_persons(self, persons: List[str], **kwargs) -> List[Dict[str, Any]]:
        return self.events.query_events(persons=persons, **kwargs)

    def query_by_tags(self, tags: List[str], **kwargs) -> List[Dict[str, Any]]:
        kw = list(tags or [])
        return self.events.query_events(keywords=kw, **kwargs)

    def query_by_location(self, location: str, **kwargs) -> List[Dict[str, Any]]:
        return self.events.query_events(location=location, **kwargs)

    # 示例数据扩展（不自动执行）
    def create_sample_data_v3(self) -> Dict[str, Any]:
        out = {}
        # 角色
        self.add_character({'name': '苏凝雪', 'prototype': '伙伴', 'narrative_role': '女主', 'tags': ['聪慧','冷静']})
        self.add_character({'name': '赵无双', 'prototype': '反派', 'narrative_role': '对手', 'tags': ['野心','阴谋']})
        self.add_character({'name': '黑衣人', 'prototype': '反派', 'narrative_role': '爪牙', 'tags': ['神秘']})
        # 关系（非对称）
        self.add_relationship('李风', '苏凝雪', {'relationship_type': '恋人', 'strength': 8, 'description': '李风倾慕'})
        self.add_relationship('苏凝雪', '李风', {'relationship_type': '朋友', 'strength': 6, 'description': '苏凝雪未明心意'})
        self.add_relationship('李风', '赵无双', {'relationship_type': '敌人', 'strength': 8})
        self.add_relationship('赵无双', '李风', {'relationship_type': '敌人', 'strength': 9})
        # 事件
        e1 = self.add_event({'name': '山路遭遇', 'timestamp': '2023-05-02T09:00:00', 'location': '天山脚下', 'tags': ['遭遇','冲突'], 'importance': 7})
        self.add_participant(e1, '李风', '主导者', 7)
        self.add_participant(e1, '黑衣人', '受害者', 5, '被击退')
        e2 = self.add_event({'name': '夜探古堡', 'timestamp': '2023-05-03T22:00:00', 'location': '古堡', 'tags': ['潜入','谜团'], 'importance': 8})
        self.add_participant(e2, '苏凝雪', '主导者', 8)
        self.add_participant(e2, '李风', '参与者', 7)
        e3 = self.add_event({'name': '宗门大比', 'timestamp': '2023-05-10T10:00:00', 'location': '宗门广场', 'tags': ['比试','转折'], 'importance': 9})
        self.add_participant(e3, '李风', '主导者', 9)
        self.add_participant(e3, '赵无双', '对手', 8)
        # 里程碑
        m1 = self.add_milestone({'character_name': '李风', 'name': '领悟剑心', 'timestamp': '2023-05-02T20:00:00', 'milestone_type': 'growth', 'importance': 8})
        self.bind_milestone_to_event(m1, e1)
        m2 = self.add_milestone({'character_name': '苏凝雪', 'name': '破除心魔', 'timestamp': '2023-05-04T08:00:00', 'milestone_type': 'achievement', 'importance': 7})
        self.bind_milestone_to_event(m2, e2)
        out['events'] = [e1, e2, e3]; out['milestones'] = [m1, m2]
        return out

# 轻量演示
if __name__ == '__main__':
    sys = NovelV3System()
    li = sys.add_character({'name': '李风', 'prototype': '英雄', 'narrative_role': '主角'})
    bai = sys.add_character({'name': '白大师', 'prototype': '导师', 'narrative_role': '配角'})
    sys.add_relationship('李风', '白大师', {'relationship_type': '师徒', 'strength': 9, 'description': '尊师重道'})
    eid = sys.add_event({'name': '初见', 'location': '天山', 'tags': ['邂逅']})
    sys.add_participant(eid, '李风', '参与者', 7)
    sys.add_participant(eid, '白大师', '参与者', 6)
    mid = sys.add_milestone({'character_name': '李风', 'name': '立志行侠仗义', 'milestone_type': 'growth'})
    sys.bind_milestone_to_event(mid, eid)
    info = sys.get_character_complete_info('李风')
    print(f"人物中央性: {info['analysis']['centrality']:.3f}")
    res = sys.query_three_elements(person='李风', matter_keywords=['邂逅'], place='天山')
    print(f"人事地查询结果数量: {len(res)}")

